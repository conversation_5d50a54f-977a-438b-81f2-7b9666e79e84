---
title: Week 3 Exercises - Basic Observable Examples
---

## Provided code

There are helper functions and pre-declared types that you may elect to use if you wish. They are not required to be used for full marks, but the questions were designed to make use of them, and solutions that do not may be more complex than necessary.

If you are stuck, try to think about how the provided code can be used in the context of the exercise.

### Running the code bundle

Open a terminal in the folder containing `package.json` (this should be the folder you just extracted).

1. Run `npm install` (installs the dependencies) and
2. `npm run dev` (starts the development server to access the code), then
3. Look in the terminal and go to the url in your browser (e.g. http://localhost:5173/).

## Hints

There are additional hints provided in the code bundle that are encoded in base64 to prevent spoilers. These can be decoded via online services such as https://www.base64decode.org/ or using the [`atob()`](https://developer.mozilla.org/en-US/docs/Web/API/atob) function available in your browser.

If you get stuck, please have a look at these hints first.
